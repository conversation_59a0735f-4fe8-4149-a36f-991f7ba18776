<template>
  <div
    class="headerbg"
    :style="style"
  >
    <div class="header-title" :style="{
      padding: `${chapterHeaderPaddingTop}px ${chapterHeaderPaddingRight}px ${chapterHeaderPaddingBottom}px ${chapterHeaderPaddingLeft}px`,
    }
    ">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { defineProps } from "vue";
import useReader from "@/store/modules/reader";

const store = useReader();
const props = defineProps({
  chapterHeaderUrl: {
    type: String,
    default: null,
  },
  chapterHeaderHeight: {
    type: Number,
    default: null,
  },
  chapterHeaderPaddingTop:{
    type: [Number,String],
    default: 0,
  },
  chapterHeaderPaddingBottom:{
    type: [Number,String],
    default: 0,
  },
  chapterHeaderPaddingLeft:{
    type: [Number,String],
    default: 0,
  },
  chapterHeaderPaddingRight:{
    type: [Number,String],
    default: 0,
  },
});

const style = computed(() => {
  let template = store.templateStyle;
  const style = {
    background: `url(${template?.chapterHeaderUrl}) no-repeat`,
    backgroundSize: '100% 100%',
    height: `${template?.chapterHeaderHeight / 2.5}px`,
    color: template?.chapterFontColor,
  }
  if (props.chapterHeaderUrl != null) {
    style.background = `url(${props.chapterHeaderUrl}) no-repeat`
  }
  if (props.chapterHeaderHeight != null) {
    style.height = `${props.chapterHeaderHeight / 2.5}px`
  }
 
  return style
})

</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  margin: 20px 0;
  display: flex;
  align-items: center;
  .header-title {
   
    outline: none;
    width: 100%;
  }
}
</style>
